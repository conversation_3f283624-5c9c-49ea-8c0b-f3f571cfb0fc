<template>
  <a-modal
    v-model:open="visible"
    title="编辑文件"
    :width="600"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="onFinish"
    >
      <!-- 文件名称 -->
      <a-form-item
        label="文件名称"
        name="fileName"
        :required="true"
      >
        <a-input
          v-model:value="formData.fileName"
          placeholder="请输入文件名称"
          size="large"
        />
      </a-form-item>

      <!-- 开启状态 -->
      <a-form-item label="开启状态">
        <a-switch
          v-model:checked="formData.isEnabled"
          size="default"
        />
      </a-form-item>

      <!-- 标签 -->
      <a-form-item label="标签">
        <div class="tags-container">
          <a-tag
            v-for="(tag, index) in formData.tags"
            :key="index"
            closable
            @close="removeTag(index)"
            class="tag-item"
          >
            {{ tag }}
          </a-tag>
          <a-input
            v-if="inputVisible"
            ref="inputRef"
            v-model:value="inputValue"
            type="text"
            size="small"
            :style="{ width: '78px' }"
            @blur="handleInputConfirm"
            @keyup.enter="handleInputConfirm"
            class="tag-input"
          />
          <a-tag
            v-else
            @click="showInput"
            class="add-tag"
          >
            <plus-outlined />
            新标签
          </a-tag>
        </div>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

// Props
interface Props {
  open?: boolean
  fileData?: {
    fileName?: string
    isEnabled?: boolean
    tags?: string[]
  }
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  fileData: () => ({
    fileName: '',
    isEnabled: true,
    tags: []
  })
})

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = ref(props.open)
const formRef = ref<FormInstance>()
const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

// 表单数据
const formData = reactive({
  fileName: '',
  isEnabled: true,
  tags: [] as string[]
})

// 表单验证规则
const rules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' }
  ]
}

// 监听 props 变化
watch(() => props.open, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置表单数据
    formData.fileName = props.fileData?.fileName || ''
    formData.isEnabled = props.fileData?.isEnabled ?? true
    formData.tags = [...(props.fileData?.tags || [])]
  }
})

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

// 添加标签相关方法
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags.includes(inputValue.value)) {
    formData.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const removeTag = (index: number) => {
  formData.tags.splice(index, 1)
}

// 表单提交
const onFinish = (values: any) => {
  console.log('表单数据:', values)
  emit('confirm', {
    fileName: formData.fileName,
    isEnabled: formData.isEnabled,
    tags: formData.tags
  })
  visible.value = false
  message.success('文件编辑成功！')
}

// 确定按钮
const handleOk = () => {
  formRef.value?.submit()
}

// 取消按钮
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-item {
  margin: 0;
}

.add-tag {
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  cursor: pointer;
  margin: 0;
}

.add-tag:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.tag-input {
  margin: 0;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-switch) {
  background-color: #1890ff;
}

:deep(.ant-switch:not(.ant-switch-checked)) {
  background-color: rgba(0, 0, 0, 0.25);
}
</style>
