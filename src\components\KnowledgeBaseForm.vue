<template>
  <div class="knowledge-base-form">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <!-- 知识库名称 -->
      <a-form-item label="知识库名称" name="name" :required="true">
        <a-input v-model:value="formData.name" placeholder="请输入" size="large" />
      </a-form-item>

      <!-- 描述 -->
      <a-form-item label="描述" name="description" :required="true">
        <a-textarea v-model:value="formData.description" placeholder="请输入" :rows="4" size="large" />
      </a-form-item>

      <!-- 所属群组 -->
      <a-form-item label="所属群组" :required="true">
        <a-tree-select
          v-model:value="formData.group"
          placeholder="请选择"
          :tree-data="groupTreeData"
          tree-default-expand-all
          :label-in-value="true"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          @change="onGroupChange"
        >
          <template #tagRender="{ label, closable, onClose }">
            <a-tag :closable="closable" @close="onClose">
              {{ groupDisplayText || label }}
            </a-tag>
          </template>
        </a-tree-select>
      </a-form-item>

      <!-- 类型 -->
      <a-form-item label="类型" name="type" :required="true">
        <a-select v-model:value="formData.type" placeholder="请选择" size="large" :options="typeOptions" />
      </a-form-item>

      <!-- 标签 -->
      <a-form-item label="标签" name="tags" :required="true">
        <a-select v-model:value="formData.tags" mode="multiple" placeholder="请选择" size="large" :options="tagOptions" />
      </a-form-item>

      <!-- 切分策略 -->
      <a-form-item label="切分策略" name="splitStrategy" :required="true">
        <a-select v-model:value="formData.splitStrategy" placeholder="请选择" size="large"
          :options="splitStrategyOptions" />
      </a-form-item>

      <!-- 向量模型 -->
      <a-form-item label="向量模型" name="vectorModel" :required="true">
        <a-select v-model:value="formData.vectorModel" placeholder="请选择" size="large" :options="vectorModelOptions" />
      </a-form-item>

      <!-- 增强策略 -->
      <a-form-item label="增强策略">
        <a-checkbox-group v-model:value="formData.enhancementStrategies">
          <a-checkbox value="questionGeneration">问题生成</a-checkbox>
          <a-checkbox value="paragraphSummary">段落假要</a-checkbox>
          <a-checkbox value="tripleExtraction">三元组知识抽取</a-checkbox>
        </a-checkbox-group>
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-button type="primary" html-type="submit" size="large">创建知识库</a-button>
          <a-button size="large" @click="resetForm">重置</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'ant-design-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  group: undefined as any,
  type: undefined,
  tags: [],
  splitStrategy: '基于目录结构',
  vectorModel: 'bge-large',
  enhancementStrategies: ['questionGeneration'] // 默认选中问题生成
})

// 存储群组的完整路径文本
const groupDisplayText = ref('')

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  group: [
    { required: true, message: '请选择所属群组', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  tags: [
    { required: true, message: '请选择标签', trigger: 'change' }
  ],
  splitStrategy: [
    { required: true, message: '请选择切分策略', trigger: 'change' }
  ],
  vectorModel: [
    { required: true, message: '请选择向量模型', trigger: 'change' }
  ]
}

// 树形选择器数据
const groupTreeData = [
  {
    title: '政府采购',
    value: 'gov-procurement',
    children: [
      {
        title: '内蒙古自治区',
        value: 'inner-mongolia',
        children: [
          { title: '呼和浩特市', value: 'hohhot' },
          { title: '包头市', value: 'baotou' },
          { title: '乌海市', value: 'wuhai' },
          { title: '赤峰市', value: 'chifeng' },
          { title: '鄂尔多斯市', value: 'ordos' }
        ]
      },
      {
        title: '福建省',
        value: 'fujian',
        children: [
          { title: '国家级', value: 'national-level' }
        ]
      }
    ]
  }
]

// 构建完整路径的辅助函数
const buildPath = (value: string, treeData: any[], path: string[] = []): string[] | null => {
  for (const node of treeData) {
    const currentPath = [...path, node.title]
    if (node.value === value) {
      return currentPath
    }
    if (node.children) {
      const result = buildPath(value, node.children, currentPath)
      if (result) return result
    }
  }
  return null
}

// 群组选择变化处理
const onGroupChange = (value: any) => {
  if (value && value.value) {
    const pathArray = buildPath(value.value, groupTreeData)
    if (pathArray) {
      groupDisplayText.value = pathArray.join(' / ')
      // 更新显示的标签
      value.label = groupDisplayText.value
    }
  }
}

const typeOptions = [
  { label: '文档库', value: 'document' },
  { label: '问答库', value: 'qa' },
  { label: '代码库', value: 'code' },
  { label: '图片库', value: 'image' }
]

const tagOptions = [
  { label: 'AI', value: 'ai' },
  { label: '机器学习', value: 'ml' },
  { label: '深度学习', value: 'dl' },
  { label: '自然语言处理', value: 'nlp' },
  { label: '计算机视觉', value: 'cv' },
  { label: '数据科学', value: 'ds' }
]

const splitStrategyOptions = [
  { label: '基于目录结构', value: '基于目录结构' },
  { label: '基于文件类型', value: '基于文件类型' },
  { label: '基于内容长度', value: '基于内容长度' },
  { label: '智能切分', value: '智能切分' }
]

const vectorModelOptions = [
  { label: 'bge-large', value: 'bge-large' },
  { label: 'bge-base', value: 'bge-base' },
  { label: 'text-embedding-ada-002', value: 'text-embedding-ada-002' },
  { label: 'm3e-large', value: 'm3e-large' },
  { label: 'm3e-base', value: 'm3e-base' }
]

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  formData.enhancementStrategies = ['questionGeneration']
}
</script>

<style scoped>
.knowledge-base-form {
  width: 600px;
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.ant-checkbox-wrapper) {
  margin-right: 0;
}
</style>
