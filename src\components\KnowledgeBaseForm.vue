<template>
  <div class="knowledge-base-form">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <!-- 知识库名称 -->
      <a-form-item label="知识库名称" name="name" :required="true">
        <a-input v-model:value="formData.name" placeholder="请输入" size="large" />
      </a-form-item>

      <!-- 描述 -->
      <a-form-item label="描述" name="description" :required="true">
        <a-textarea v-model:value="formData.description" placeholder="请输入" :rows="4" size="large" />
      </a-form-item>

      <!-- 所属群组 -->
      <a-form-item label="所属群组" name="group" :required="true">
        <a-select v-model:value="formData.group" placeholder="请选择" size="large" :options="groupOptions" />
      </a-form-item>

      <!-- 类型 -->
      <a-form-item label="类型" name="type" :required="true">
        <a-select v-model:value="formData.type" placeholder="请选择" size="large" :options="typeOptions" />
      </a-form-item>

      <!-- 标签 -->
      <a-form-item label="标签" name="tags" :required="true">
        <a-select v-model:value="formData.tags" mode="multiple" placeholder="请选择" size="large" :options="tagOptions" />
      </a-form-item>

      <!-- 切分策略 -->
      <a-form-item label="切分策略" name="splitStrategy" :required="true">
        <a-select v-model:value="formData.splitStrategy" placeholder="请选择" size="large"
          :options="splitStrategyOptions" />
      </a-form-item>

      <!-- 向量模型 -->
      <a-form-item label="向量模型" name="vectorModel" :required="true">
        <a-select v-model:value="formData.vectorModel" placeholder="请选择" size="large" :options="vectorModelOptions" />
      </a-form-item>

      <!-- 增强策略 -->
      <a-form-item label="增强策略">
        <a-checkbox-group v-model:value="formData.enhancementStrategies">
          <a-checkbox value="questionGeneration">问题生成</a-checkbox>
          <a-checkbox value="paragraphSummary">段落假要</a-checkbox>
          <a-checkbox value="tripleExtraction">三元组知识抽取</a-checkbox>
        </a-checkbox-group>
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-button type="primary" html-type="submit" size="large">创建知识库</a-button>
          <a-button size="large" @click="resetForm">重置</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'ant-design-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  group: undefined,
  type: undefined,
  tags: [],
  splitStrategy: '基于目录结构',
  vectorModel: 'bge-large',
  enhancementStrategies: ['questionGeneration'] // 默认选中问题生成
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  group: [
    { required: true, message: '请选择所属群组', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  tags: [
    { required: true, message: '请选择标签', trigger: 'change' }
  ],
  splitStrategy: [
    { required: true, message: '请选择切分策略', trigger: 'change' }
  ],
  vectorModel: [
    { required: true, message: '请选择向量模型', trigger: 'change' }
  ]
}

// 选项数据
const groupOptions = [
  { label: '默认群组', value: 'default' },
  { label: '技术文档', value: 'tech' },
  { label: '产品文档', value: 'product' },
  { label: '运营文档', value: 'operation' }
]

const typeOptions = [
  { label: '文档库', value: 'document' },
  { label: '问答库', value: 'qa' },
  { label: '代码库', value: 'code' },
  { label: '图片库', value: 'image' }
]

const tagOptions = [
  { label: 'AI', value: 'ai' },
  { label: '机器学习', value: 'ml' },
  { label: '深度学习', value: 'dl' },
  { label: '自然语言处理', value: 'nlp' },
  { label: '计算机视觉', value: 'cv' },
  { label: '数据科学', value: 'ds' }
]

const splitStrategyOptions = [
  { label: '基于目录结构', value: '基于目录结构' },
  { label: '基于文件类型', value: '基于文件类型' },
  { label: '基于内容长度', value: '基于内容长度' },
  { label: '智能切分', value: '智能切分' }
]

const vectorModelOptions = [
  { label: 'bge-large', value: 'bge-large' },
  { label: 'bge-base', value: 'bge-base' },
  { label: 'text-embedding-ada-002', value: 'text-embedding-ada-002' },
  { label: 'm3e-large', value: 'm3e-large' },
  { label: 'm3e-base', value: 'm3e-base' }
]

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  formData.enhancementStrategies = ['questionGeneration']
}
</script>

<style scoped>
.knowledge-base-form {
  width: 600px;
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.ant-checkbox-wrapper) {
  margin-right: 0;
}
</style>
