<template>
  <div class="file-management">
    <a-card title="文件管理" :bordered="false">
      <!-- 文件列表 -->
      <a-table
        :columns="columns"
        :data-source="fileList"
        :pagination="false"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'isEnabled'">
            <a-switch
              v-model:checked="record.isEnabled"
              size="small"
              disabled
            />
          </template>
          <template v-else-if="column.key === 'tags'">
            <a-tag
              v-for="tag in record.tags"
              :key="tag"
              color="blue"
            >
              {{ tag }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button
              type="link"
              @click="editFile(record)"
            >
              编辑
            </a-button>
          </template>
        </template>
      </a-table>

      <!-- 添加文件按钮 -->
      <div style="margin-top: 16px;">
        <a-button type="primary" @click="addFile">
          添加文件
        </a-button>
      </div>
    </a-card>

    <!-- 编辑文件弹窗 -->
    <EditFileModal
      v-model:open="modalVisible"
      :file-data="currentFileData"
      @confirm="handleFileConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import EditFileModal from './EditFileModal.vue'

// 表格列定义
const columns = [
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
  },
  {
    title: '开启状态',
    dataIndex: 'isEnabled',
    key: 'isEnabled',
    width: 120,
  },
  {
    title: '标签',
    dataIndex: 'tags',
    key: 'tags',
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
  },
]

// 文件列表数据
const fileList = reactive([
  {
    id: 1,
    fileName: '政府采购框架协议采购方式管理暂行办法',
    isEnabled: true,
    tags: ['标签1', '标签2', '标签3']
  },
  {
    id: 2,
    fileName: '示例文件2.pdf',
    isEnabled: false,
    tags: ['文档', '重要']
  },
  {
    id: 3,
    fileName: '示例文件3.docx',
    isEnabled: true,
    tags: ['草稿']
  }
])

// 弹窗相关
const modalVisible = ref(false)
const currentFileData = ref({})
const editingIndex = ref(-1)

// 编辑文件
const editFile = (record: any) => {
  currentFileData.value = {
    fileName: record.fileName,
    isEnabled: record.isEnabled,
    tags: [...record.tags]
  }
  editingIndex.value = fileList.findIndex(item => item.id === record.id)
  modalVisible.value = true
}

// 添加文件
const addFile = () => {
  currentFileData.value = {
    fileName: '',
    isEnabled: true,
    tags: []
  }
  editingIndex.value = -1
  modalVisible.value = true
}

// 确认编辑
const handleFileConfirm = (data: any) => {
  if (editingIndex.value >= 0) {
    // 编辑现有文件
    const file = fileList[editingIndex.value]
    file.fileName = data.fileName
    file.isEnabled = data.isEnabled
    file.tags = data.tags
  } else {
    // 添加新文件
    const newFile = {
      id: Date.now(),
      fileName: data.fileName,
      isEnabled: data.isEnabled,
      tags: data.tags
    }
    fileList.push(newFile)
  }
}
</script>

<style scoped>
.file-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

:deep(.ant-table-tbody > tr > td) {
  vertical-align: top;
}
</style>
