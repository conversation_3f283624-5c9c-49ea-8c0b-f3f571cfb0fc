<script setup lang="ts">
import { ref } from 'vue'
import KnowledgeBaseForm from './components/KnowledgeBaseForm.vue'
import FileManagement from './components/FileManagement.vue'

const currentTab = ref('knowledge-base')

const tabItems = [
  { key: 'knowledge-base', label: '知识库表单' },
  { key: 'file-management', label: '文件管理' }
]
</script>

<template>
  <div id="app">
    <a-tabs v-model:activeKey="currentTab" :items="tabItems" size="large" style="padding: 24px;">
      <template #tabBarExtraContent>
        <span style="color: #666;">Ant Design Vue 表单演示</span>
      </template>
    </a-tabs>

    <div class="tab-content">
      <KnowledgeBaseForm v-if="currentTab === 'knowledge-base'" />
      <FileManagement v-if="currentTab === 'file-management'" />
    </div>
  </div>
</template>

<style>
#app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

body {
  margin: 0;
  padding: 0;
}

.tab-content {
  padding: 0 24px 24px;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 0;
}
</style>
